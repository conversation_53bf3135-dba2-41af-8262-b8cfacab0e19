package com.datascope.app.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.StrPool;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Function;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ComparisonOperator;
import net.sf.jsqlparser.expression.operators.relational.ExistsExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.util.TablesNamesFinder;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SQL解析工具类，用于提取SQL中的表名和字段
 *
 * <AUTHOR>
 */
@Slf4j
public class SqlParserUtil {

    // 匹配MyBatis风格的占位符 #{xxx}
    private static final Pattern MYBATIS_PATTERN = Pattern.compile("#\\{([^}]+)\\}");

    // 匹配JPA风格的占位符 :xxx
    private static final Pattern JPA_PATTERN = Pattern.compile(":(\\w+)");

    /**
     * 解析SQL，提取表名和对应的字段
     *
     * @param sql SQL语句
     * @return 表名和字段的映射关系
     */
    public static Map<String, Set<String>> extractTableAndColumns(String sql) {
        // 预处理SQL，替换占位符
        String processedSql = preprocessSql(sql);

        Map<String, Set<String>> result = new HashMap<>();
        try {
            Statement statement = CCJSqlParserUtil.parse(processedSql);
            if (statement instanceof Select) {
                Select select = (Select) statement;
                SelectBody selectBody = select.getSelectBody();

                // 提取表名
                TablesNamesFinder tablesNamesFinder = new TablesNamesFinder();
                List<String> tableNames = tablesNamesFinder.getTableList(select);

                // 初始化结果集，去掉库名前缀，只保留纯表名
                tableNames.forEach(tableName -> {
                    String pureTableName = extractPureTableName(tableName);
                    result.put(pureTableName, new HashSet<>());
                });

                // 处理查询主体
                if (selectBody instanceof PlainSelect) {
                    processPlainSelect((PlainSelect) selectBody, result);
                } else if (selectBody instanceof SetOperationList) {
                    SetOperationList setOperationList = (SetOperationList) selectBody;
                    for (SelectBody selectBodyItem : setOperationList.getSelects()) {
                        if (selectBodyItem instanceof PlainSelect) {
                            processPlainSelect((PlainSelect) selectBodyItem, result);
                        }
                    }
                }
            }
        } catch (JSQLParserException e) {
            log.error("SQL解析失败: {}", e.getMessage());
        }
        if (CollUtil.isNotEmpty(result)) {
            Map<String, Set<String>> map = result.entrySet().stream().filter(r -> !r.getKey().contains(StrPool.DOT))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1));
            log.info("SqlParserUtil extractTableAndColumns result: {}", map);
            return map;
        }
        return result;
    }

    /**
     * 提取纯表名，去掉库名前缀
     *
     * @param fullTableName 完整表名（可能包含库名）
     * @return 纯表名
     */
    private static String extractPureTableName(String fullTableName) {
        if (fullTableName == null) {
            return null;
        }
        // 如果包含点号，取点号后面的部分作为表名
        int dotIndex = fullTableName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fullTableName.length() - 1) {
            return fullTableName.substring(dotIndex + 1);
        }
        return fullTableName;
    }

    /**
     * 预处理SQL，替换占位符为临时值以便解析
     *
     * @param sql 原始SQL
     * @return 处理后的SQL
     */
    private static String preprocessSql(String sql) {
        // 移除DB2特有的 "with ur" 子句
        String result = sql.replaceAll("(?i)\\s+with\\s+ur\\s*$", "");
        
        // 替换MyBatis风格的占位符 #{xxx} 为 'placeholder_value'
        result = replaceParametersOutsideQuotes(result, MYBATIS_PATTERN, "'placeholder_value'");

        // 替换JPA风格的占位符 :xxx 为 'placeholder_value'
        result = replaceParametersOutsideQuotes(result, JPA_PATTERN, "'placeholder_value'");

        return result;
    }

    /**
     * 替换不在字符串字面量中的参数占位符
     *
     * @param sql 原始SQL
     * @param pattern 参数匹配模式
     * @param replacement 替换值
     * @return 处理后的SQL
     */
    private static String replaceParametersOutsideQuotes(String sql, Pattern pattern, String replacement) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        Matcher matcher = pattern.matcher(sql);
        StringBuilder result = new StringBuilder();
        int lastEnd = 0;

        while (matcher.find()) {
            int start = matcher.start();
            int end = matcher.end();

            // 检查参数是否在字符串字面量中
            if (isParameterInQuotes(sql, start)) {
                // 在字符串中，保持原样
                result.append(sql.substring(lastEnd, end));
            } else {
                // 不在字符串中，进行替换
                result.append(sql.substring(lastEnd, start));
                result.append(replacement);
            }
            lastEnd = end;
        }

        // 添加剩余部分
        result.append(sql.substring(lastEnd));
        return result.toString();
    }

    /**
     * 检查参数是否在字符串字面量中（单引号或双引号）
     *
     * @param sql SQL语句
     * @param position 参数位置
     * @return 是否在字符串字面量中
     */
    private static boolean isParameterInQuotes(String sql, int position) {
        // 检查参数是否在字符串字面量内部
        Pattern stringLiteralPattern = Pattern.compile("(['\"]).*?\\1");
        Matcher stringLiteralMatcher = stringLiteralPattern.matcher(sql);
        
        while (stringLiteralMatcher.find()) {
            int start = stringLiteralMatcher.start();
            int end = stringLiteralMatcher.end();
            
            // 检查参数位置是否在当前字符串字面量的范围内
            // 使用 <= 而不是 < 来确保参数完全在字符串内
            if (position > start && position < end) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理普通查询
     *
     * @param plainSelect 普通查询对象
     * @param result      结果集
     */
    private static void processPlainSelect(PlainSelect plainSelect, Map<String, Set<String>> result) {
        List<SelectItem> selectItems = plainSelect.getSelectItems();

        // 获取FROM子句中的表
        FromItem fromItem = plainSelect.getFromItem();
        String mainTable = extractTableName(fromItem);
        String pureMainTable = extractPureTableName(mainTable);

        // 处理JOIN子句
        List<Join> joins = plainSelect.getJoins();
        Map<String, String> tableAliasMap = new HashMap<>();

        // 记录表名和别名的映射关系
        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            if (table.getAlias() != null) {
                // 别名映射到纯表名
                tableAliasMap.put(table.getAlias().getName(), extractPureTableName(table.getName()));
            }
        } else if (fromItem instanceof SubSelect) {
            // 处理FROM子句中的子查询
            processSubSelect((SubSelect) fromItem, result);
        }

        // 处理JOIN中的表别名
        if (joins != null) {
            for (Join join : joins) {
                FromItem joinItem = join.getRightItem();
                if (joinItem instanceof Table) {
                    Table table = (Table) joinItem;
                    if (table.getAlias() != null) {
                        // 别名映射到纯表名
                        tableAliasMap.put(table.getAlias().getName(), extractPureTableName(table.getName()));
                    }
                } else if (joinItem instanceof SubSelect) {
                    // 处理JOIN子句中的子查询
                    processSubSelect((SubSelect) joinItem, result);
                }

                // 处理JOIN条件中的字段
                Expression onExpression = join.getOnExpression();
                if (onExpression != null) {
                    processJoinCondition(onExpression, tableAliasMap, result);
                }
            }
        }

        // 处理WHERE条件中的字段
        Expression whereExpression = plainSelect.getWhere();
        if (whereExpression != null) {
            processWhereCondition(whereExpression, tableAliasMap, result);
        }

        // 处理ORDER BY子句中的字段
        List<OrderByElement> orderByElements = plainSelect.getOrderByElements();
        if (orderByElements != null) {
            for (OrderByElement orderByElement : orderByElements) {
                Expression expression = orderByElement.getExpression();
                if (expression instanceof Column) {
                    processColumnInCondition((Column) expression, tableAliasMap, result);
                } else if (expression instanceof SubSelect) {
                    processSubSelect((SubSelect) expression, result);
                } else if (expression instanceof Function) {
                    Function function = (Function) expression;
                    if (function.getParameters() != null) {
                        for (Expression param : function.getParameters().getExpressions()) {
                            if (param instanceof Column) {
                                processColumnInCondition((Column) param, tableAliasMap, result);
                            } else if (param instanceof SubSelect) {
                                processSubSelect((SubSelect) param, result);
                            }
                        }
                    }
                }
            }
        }

        // 处理GROUP BY子句中的字段
        GroupByElement groupBy = plainSelect.getGroupBy();
        if (groupBy != null && groupBy.getGroupByExpressions() != null) {
            for (Expression expression : groupBy.getGroupByExpressions()) {
                if (expression instanceof Column) {
                    processColumnInCondition((Column) expression, tableAliasMap, result);
                } else if (expression instanceof SubSelect) {
                    processSubSelect((SubSelect) expression, result);
                } else if (expression instanceof Function) {
                    Function function = (Function) expression;
                    if (function.getParameters() != null) {
                        for (Expression param : function.getParameters().getExpressions()) {
                            if (param instanceof Column) {
                                processColumnInCondition((Column) param, tableAliasMap, result);
                            } else if (param instanceof SubSelect) {
                                processSubSelect((SubSelect) param, result);
                            }
                        }
                    }
                }
            }
        }

        // 处理HAVING子句中的字段
        Expression havingExpression = plainSelect.getHaving();
        if (havingExpression != null) {
            processWhereCondition(havingExpression, tableAliasMap, result);
        }

        // 处理选择的列
        for (SelectItem selectItem : selectItems) {
            if (selectItem instanceof SelectExpressionItem) {
                SelectExpressionItem expressionItem = (SelectExpressionItem) selectItem;
                Expression expression = expressionItem.getExpression();

                if (expression instanceof Column) {
                    Column column = (Column) expression;
                    String columnName = column.getColumnName();
                    String tableName = null;

                    // 处理表名
                    if (column.getTable() != null && column.getTable().getName() != null) {
                        String tableReference = column.getTable().getName();
                        // 检查是否是别名
                        if (tableAliasMap.containsKey(tableReference)) {
                            tableName = tableAliasMap.get(tableReference);
                        } else {
                            // 使用纯表名
                            tableName = extractPureTableName(tableReference);
                        }
                    } else {
                        // 如果列没有指定表名，默认使用主表的纯表名
                        tableName = pureMainTable;
                    }

                    // 添加到结果集
                    if (tableName != null && result.containsKey(tableName)) {
                        result.get(tableName).add(columnName);
                    }
                } else if (expression instanceof SubSelect) {
                    // 处理SELECT子句中的子查询
                    processSubSelect((SubSelect) expression, result);
                } else if (expression instanceof Function) {
                    // 处理函数，例如 COUNT(column)
                    Function function = (Function) expression;
                    if (function.getParameters() != null) {
                        for (Expression param : function.getParameters().getExpressions()) {
                            if (param instanceof Column) {
                                processColumnInCondition((Column) param, tableAliasMap, result);
                            } else if (param instanceof SubSelect) {
                                processSubSelect((SubSelect) param, result);
                            }
                        }
                    }
                }
            } else if (selectItem instanceof AllColumns) {
                // 处理 SELECT * 的情况
                // 将所有表标记为使用了全部字段
                for (String tableName : result.keySet()) {
                    result.get(tableName).add("*");
                }
            } else if (selectItem instanceof AllTableColumns) {
                // 处理 SELECT table.* 的情况
                AllTableColumns allTableColumns = (AllTableColumns) selectItem;
                String tableReference = allTableColumns.getTable().getName();
                String tableName;

                // 检查是否是别名
                if (tableAliasMap.containsKey(tableReference)) {
                    tableName = tableAliasMap.get(tableReference);
                } else {
                    // 使用纯表名
                    tableName = extractPureTableName(tableReference);
                }

                if (result.containsKey(tableName)) {
                    result.get(tableName).add("*");
                }
            }
        }
    }

    /**
     * 处理子查询
     *
     * @param subSelect 子查询对象
     * @param result 结果集
     */
    private static void processSubSelect(SubSelect subSelect, Map<String, Set<String>> result) {
        SelectBody selectBody = subSelect.getSelectBody();
        if (selectBody instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) selectBody;

            // 处理子查询中的表别名
            Map<String, String> subQueryTableAliasMap = new HashMap<>();

            // 处理FROM子句
            FromItem fromItem = plainSelect.getFromItem();
            if (fromItem instanceof Table) {
                Table table = (Table) fromItem;
                String tableName = extractPureTableName(table.getName());

                // 确保表存在于结果集中
                if (!result.containsKey(tableName)) {
                    result.put(tableName, new HashSet<>());
                }

                // 记录别名
                if (table.getAlias() != null) {
                    subQueryTableAliasMap.put(table.getAlias().getName(), tableName);
                }
            }

            // 处理JOIN子句
            List<Join> joins = plainSelect.getJoins();
            if (joins != null) {
                for (Join join : joins) {
                    FromItem joinItem = join.getRightItem();
                    if (joinItem instanceof Table) {
                        Table table = (Table) joinItem;
                        String tableName = extractPureTableName(table.getName());

                        // 确保表存在于结果集中
                        if (!result.containsKey(tableName)) {
                            result.put(tableName, new HashSet<>());
                        }

                        // 记录别名
                        if (table.getAlias() != null) {
                            subQueryTableAliasMap.put(table.getAlias().getName(), tableName);
                        }
                    }

                    // 处理JOIN条件
                    Expression onExpression = join.getOnExpression();
                    if (onExpression != null) {
                        processJoinCondition(onExpression, subQueryTableAliasMap, result);
                    }
                }
            }

            // 处理WHERE条件
            Expression whereExpression = plainSelect.getWhere();
            if (whereExpression != null) {
                processWhereCondition(whereExpression, subQueryTableAliasMap, result);
            }

            // 处理ORDER BY子句中的字段
            List<OrderByElement> orderByElements = plainSelect.getOrderByElements();
            if (orderByElements != null) {
                for (OrderByElement orderByElement : orderByElements) {
                    Expression expression = orderByElement.getExpression();
                    if (expression instanceof Column) {
                        processColumnInCondition((Column) expression, subQueryTableAliasMap, result);
                    } else if (expression instanceof SubSelect) {
                        processSubSelect((SubSelect) expression, result);
                    } else if (expression instanceof Function) {
                        Function function = (Function) expression;
                        if (function.getParameters() != null) {
                            for (Expression param : function.getParameters().getExpressions()) {
                                if (param instanceof Column) {
                                    processColumnInCondition((Column) param, subQueryTableAliasMap, result);
                                } else if (param instanceof SubSelect) {
                                    processSubSelect((SubSelect) param, result);
                                }
                            }
                        }
                    }
                }
            }

            // 处理GROUP BY子句中的字段
            GroupByElement groupBy = plainSelect.getGroupBy();
            if (groupBy != null && groupBy.getGroupByExpressions() != null) {
                for (Expression expression : groupBy.getGroupByExpressions()) {
                    if (expression instanceof Column) {
                        processColumnInCondition((Column) expression, subQueryTableAliasMap, result);
                    } else if (expression instanceof SubSelect) {
                        processSubSelect((SubSelect) expression, result);
                    } else if (expression instanceof Function) {
                        Function function = (Function) expression;
                        if (function.getParameters() != null) {
                            for (Expression param : function.getParameters().getExpressions()) {
                                if (param instanceof Column) {
                                    processColumnInCondition((Column) param, subQueryTableAliasMap, result);
                                } else if (param instanceof SubSelect) {
                                    processSubSelect((SubSelect) param, result);
                                }
                            }
                        }
                    }
                }
            }

            // 处理HAVING子句中的字段
            Expression havingExpression = plainSelect.getHaving();
            if (havingExpression != null) {
                processWhereCondition(havingExpression, subQueryTableAliasMap, result);
            }

            // 处理SELECT项
            List<SelectItem> selectItems = plainSelect.getSelectItems();
            for (SelectItem selectItem : selectItems) {
                if (selectItem instanceof SelectExpressionItem) {
                    SelectExpressionItem expressionItem = (SelectExpressionItem) selectItem;
                    Expression expression = expressionItem.getExpression();

                    if (expression instanceof Column) {
                        processColumnInCondition((Column) expression, subQueryTableAliasMap, result);
                    } else if (expression instanceof Function) {
                        // 处理函数，例如 COUNT(column)
                        Function function = (Function) expression;
                        if (function.getParameters() != null) {
                            for (Expression param : function.getParameters().getExpressions()) {
                                if (param instanceof Column) {
                                    processColumnInCondition((Column) param, subQueryTableAliasMap, result);
                                }
                            }
                        }
                    }
                }
            }
        } else if (selectBody instanceof SetOperationList) {
            SetOperationList setOperationList = (SetOperationList) selectBody;
            for (SelectBody selectBodyItem : setOperationList.getSelects()) {
                if (selectBodyItem instanceof PlainSelect) {
                    processPlainSelect((PlainSelect) selectBodyItem, result);
                }
            }
        }
    }

    /**
     * 处理JOIN条件中的字段
     *
     * @param expression JOIN条件表达式
     * @param tableAliasMap 表别名映射
     * @param result 结果集
     */
    private static void processJoinCondition(Expression expression, Map<String, String> tableAliasMap, Map<String, Set<String>> result) {
        if (expression instanceof EqualsTo) {
            EqualsTo equalsTo = (EqualsTo) expression;

            // 处理左侧表达式
            if (equalsTo.getLeftExpression() instanceof Column) {
                processColumnInCondition((Column) equalsTo.getLeftExpression(), tableAliasMap, result);
            } else if (equalsTo.getLeftExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) equalsTo.getLeftExpression(), result);
            }

            // 处理右侧表达式
            if (equalsTo.getRightExpression() instanceof Column) {
                processColumnInCondition((Column) equalsTo.getRightExpression(), tableAliasMap, result);
            } else if (equalsTo.getRightExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) equalsTo.getRightExpression(), result);
            }
        } else if (expression instanceof AndExpression) {
            AndExpression andExpression = (AndExpression) expression;
            processJoinCondition(andExpression.getLeftExpression(), tableAliasMap, result);
            processJoinCondition(andExpression.getRightExpression(), tableAliasMap, result);
        } else if (expression instanceof OrExpression) {
            OrExpression orExpression = (OrExpression) expression;
            processJoinCondition(orExpression.getLeftExpression(), tableAliasMap, result);
            processJoinCondition(orExpression.getRightExpression(), tableAliasMap, result);
        } else if (expression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            if (binaryExpression.getLeftExpression() instanceof Column) {
                processColumnInCondition((Column) binaryExpression.getLeftExpression(), tableAliasMap, result);
            } else if (binaryExpression.getLeftExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) binaryExpression.getLeftExpression(), result);
            }

            if (binaryExpression.getRightExpression() instanceof Column) {
                processColumnInCondition((Column) binaryExpression.getRightExpression(), tableAliasMap, result);
            } else if (binaryExpression.getRightExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) binaryExpression.getRightExpression(), result);
            }
        }
    }

    /**
     * 处理WHERE条件中的字段
     *
     * @param expression WHERE条件表达式
     * @param tableAliasMap 表别名映射
     * @param result 结果集
     */
    private static void processWhereCondition(Expression expression, Map<String, String> tableAliasMap, Map<String, Set<String>> result) {
        if (expression instanceof ExistsExpression) {
            // 处理EXISTS表达式
            ExistsExpression existsExpression = (ExistsExpression) expression;
            if (existsExpression.getRightExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) existsExpression.getRightExpression(), result);
            }
        } else if (expression instanceof ComparisonOperator) {
            ComparisonOperator comparisonOperator = (ComparisonOperator) expression;

            // 处理左侧表达式
            if (comparisonOperator.getLeftExpression() instanceof Column) {
                processColumnInCondition((Column) comparisonOperator.getLeftExpression(), tableAliasMap, result);
            } else if (comparisonOperator.getLeftExpression() instanceof BinaryExpression) {
                processWhereCondition(comparisonOperator.getLeftExpression(), tableAliasMap, result);
            } else if (comparisonOperator.getLeftExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) comparisonOperator.getLeftExpression(), result);
            }

            // 处理右侧表达式
            if (comparisonOperator.getRightExpression() instanceof Column) {
                processColumnInCondition((Column) comparisonOperator.getRightExpression(), tableAliasMap, result);
            } else if (comparisonOperator.getRightExpression() instanceof BinaryExpression) {
                processWhereCondition(comparisonOperator.getRightExpression(), tableAliasMap, result);
            } else if (comparisonOperator.getRightExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) comparisonOperator.getRightExpression(), result);
            }
        } else if (expression instanceof InExpression) {
            // 处理IN表达式
            InExpression inExpression = (InExpression) expression;

            // 处理左侧表达式（通常是列）
            if (inExpression.getLeftExpression() instanceof Column) {
                processColumnInCondition((Column) inExpression.getLeftExpression(), tableAliasMap, result);
            }

            // 处理右侧表达式（可能是子查询或值列表）
            if (inExpression.getRightItemsList() instanceof SubSelect) {
                processSubSelect((SubSelect) inExpression.getRightItemsList(), result);
            } else if (inExpression.getRightItemsList() instanceof ExpressionList) {
                // 处理IN (value1, value2, ...)
                ExpressionList expressionList = (ExpressionList) inExpression.getRightItemsList();
                for (Expression item : expressionList.getExpressions()) {
                    if (item instanceof Column) {
                        processColumnInCondition((Column) item, tableAliasMap, result);
                    } else if (item instanceof SubSelect) {
                        processSubSelect((SubSelect) item, result);
                    }
                }
            }
        } else if (expression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) expression;

            // 处理左侧表达式
            if (binaryExpression.getLeftExpression() instanceof Column) {
                processColumnInCondition((Column) binaryExpression.getLeftExpression(), tableAliasMap, result);
            } else if (binaryExpression.getLeftExpression() instanceof BinaryExpression) {
                processWhereCondition(binaryExpression.getLeftExpression(), tableAliasMap, result);
            } else if (binaryExpression.getLeftExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) binaryExpression.getLeftExpression(), result);
            } else if (binaryExpression.getLeftExpression() instanceof ExistsExpression) {
                processWhereCondition(binaryExpression.getLeftExpression(), tableAliasMap, result);
            }

            // 处理右侧表达式
            if (binaryExpression.getRightExpression() instanceof Column) {
                processColumnInCondition((Column) binaryExpression.getRightExpression(), tableAliasMap, result);
            } else if (binaryExpression.getRightExpression() instanceof BinaryExpression) {
                processWhereCondition(binaryExpression.getRightExpression(), tableAliasMap, result);
            } else if (binaryExpression.getRightExpression() instanceof SubSelect) {
                processSubSelect((SubSelect) binaryExpression.getRightExpression(), result);
            } else if (binaryExpression.getRightExpression() instanceof ExistsExpression) {
                processWhereCondition(binaryExpression.getRightExpression(), tableAliasMap, result);
            }
        }
    }

    /**
     * 处理条件中的列
     *
     * @param column 列对象
     * @param tableAliasMap 表别名映射
     * @param result 结果集
     */
    private static void processColumnInCondition(Column column, Map<String, String> tableAliasMap, Map<String, Set<String>> result) {
        String columnName = column.getColumnName();
        String tableName = null;

        // 处理表名
        if (column.getTable() != null && column.getTable().getName() != null) {
            String tableReference = column.getTable().getName();
            // 检查是否是别名
            if (tableAliasMap.containsKey(tableReference)) {
                tableName = tableAliasMap.get(tableReference);
            } else {
                // 如果不是别名，使用纯表名
                tableName = extractPureTableName(tableReference);

                // 检查是否是单字母或短别名（通常是子查询中的表别名）
                if (tableReference.length() <= 2 && !result.containsKey(tableName)) {
                    // 这可能是一个别名，但我们没有在别名映射中找到它
                    // 在这种情况下，我们不添加这个表到结果集
                    return;
                }
            }
        }

        // 添加到结果集
        if (tableName != null) {
            // 确保表存在于结果集中
            if (!result.containsKey(tableName)) {
                result.put(tableName, new HashSet<>());
            }
            result.get(tableName).add(columnName);
        }
    }

    /**
     * 提取表名
     *
     * @param fromItem FROM子句项
     * @return 表名
     */
    private static String extractTableName(FromItem fromItem) {
        if (fromItem instanceof Table) {
            return ((Table) fromItem).getName();
        }
        return null;
    }

    /**
     * 格式化输出解析结果
     *
     * @param tableColumnsMap 表名和字段的映射关系
     * @return 格式化后的字符串
     */
    public static String formatResult(Map<String, Set<String>> tableColumnsMap) {
        StringBuilder sb = new StringBuilder();
        sb.append("SQL解析结果:\n");

        tableColumnsMap.forEach((tableName, columns) -> {
            sb.append("表名: ").append(tableName).append("\n");
            sb.append("字段: ");
            if (columns.contains("*")) {
                sb.append("* (所有字段)");
            } else {
                sb.append(String.join(", ", columns));
            }
            sb.append("\n\n");
        });

        return sb.toString();
    }

    /**
     * SQL解析结果
     */
    @Data
    public static class SqlParseResult {
        private Map<String, Set<String>> tableColumns;

        public SqlParseResult(Map<String, Set<String>> tableColumns) {
            this.tableColumns = tableColumns;
        }

        @Override
        public String toString() {
            return formatResult(tableColumns);
        }
    }

    /**
     * 解析SQL并返回结构化结果
     *
     * @param sql SQL语句
     * @return 解析结果对象
     */
    public static SqlParseResult parse(String sql) {
        return new SqlParseResult(extractTableAndColumns(sql));
    }
}
