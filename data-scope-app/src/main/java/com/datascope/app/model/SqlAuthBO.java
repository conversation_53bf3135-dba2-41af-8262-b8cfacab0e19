package com.datascope.app.model;

import com.datascope.app.dto.query.QueryFieldDTO;
import com.datascope.app.entity.Column;
import com.datascope.app.entity.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class SqlAuthBO implements Serializable {

    private Map<String, Set<String>> params;

    private List<Table> tables;

    private List<QueryFieldDTO> fields;

    private Set<QueryFieldDTO> encryptedFields;

    private Map<String, String> columnFormatMap;

}
